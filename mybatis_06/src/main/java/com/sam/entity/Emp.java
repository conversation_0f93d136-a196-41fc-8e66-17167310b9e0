package com.sam.entity;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-21 14:18
 */
@Data
@ToString(exclude = "dept")
public class Emp {
    //员工id
    private Integer empId;
    //员工名称
    private String empName;
    //员工年龄
    private Integer empAge;
    //员工性别
    private Integer empSex;
    //员工邮箱
    private String empEmail;
    //员工地址
    private String empAddress;
    //员工所属部门，和部门表构成一对一的关系，一个员工只能在一个部门 （d_id）
    private Dept dept;

}
