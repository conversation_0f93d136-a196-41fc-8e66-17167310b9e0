package com.sam.mapper;

import com.sam.entity.Emp;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface EmpMapper {

    //根据ID查询一个员工的信息，并且将他的部门查询出来
    @Select("select * from tb_emp where emp_id=#{empId}")
    @Results(id="empMap",value = {
            //主键映射。id只能有一个
//            @Result(id = true,property = "empId",column = "emp_id"),
//            @Result(property = "empName",column = "emp_name"),
//            @Result(property = "empAge",column = "emp_age"),
//            @Result(property = "empSex",column = "emp_sex"),
//            @Result(property = "empEmail",column = "emp_email"),
            @Result(property = "dept",column = "d_id",
            one = @One(select = "com.sam.mapper.DeptMapper.selectDept"))
    })
    Emp selectEmpAndDeptById(@Param("empId") Integer empId);

    //根据部门编号查询部门下的所有员工信息
    @Results(id = "deptMap",value = {
            @Result(id = true,property = "deptId",column = "dept_id"),
            @Result(property = "deptName",column = "dept_name"),
            @Result(property = "empList",column = "dept_id",
            many = @Many(select = "com.sam.mapper.EmpMapper.selectEmp"))
    })
    //select * from tb_emp where d_id=#{deptId}
    @Select("select * from tb_emp where d_id=#{deptId}")
    List<Emp> selectEmp(@Param("deptId") Integer deptId);

}
