package com.sam.mapper;

import com.sam.entity.Dept;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface DeptMapper {
    // 根据部门编号查询部门（单表查询）
    @Select("select * from tb_dept where dept_id=#{deptId}")
    Dept selectDept(@Param("deptId") Integer deptId);

    // 根据部门编号查询部门，并且查询出部门下的所有员工信息
    @Select("select * from tb_dept where dept_id=#{deptId}")
    Dept selectDeptAndEmpList(@Param("deptId") Integer deptId);

}
