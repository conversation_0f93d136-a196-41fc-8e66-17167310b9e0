package com.sam.mapper;

import com.sam.entity.Emp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class EmpMapperTest {

    private SqlSession sqlSession;
    private EmpMapper empMapper;

    @Before
    public void setUp() throws Exception {
        System.out.println("----开始测试----");
        System.out.println("1.加载mybatis-config.xml和UserMapper.xml文件");
        String resource = "mybatis-config.xml";
        System.out.println("2.构建SqlSessionFactory(管理数据库会话的核心对象)");
        SqlSessionFactory sqlSessionFactory =
                new SqlSessionFactoryBuilder().build(Resources.getResourceAsStream(resource));
        System.out.println("3.从SqlSessionFactory中打开一个SqlSession");
        sqlSession = sqlSessionFactory.openSession();
        System.out.println("4.使用SqlSession创建EmpMapper接口的代理对象");
        empMapper = sqlSession.getMapper(EmpMapper.class);
    }

    @After
    public void tearDown() throws Exception {
        System.out.println("6.调用 SqlSession.commit() 手动提交事务");
        sqlSession.commit();
        System.out.println("7.调用 SqlSession.close() 释放连接（归还连接池）");
        sqlSession.close();
        System.out.println("----结束测试----");
    }

    @Test
    public void selectEmpAndDept() {
        System.out.println("5.调用EmpMapper接口中的方法，执行selectEmpAndDept查询操作");
        Emp emp = empMapper.selectEmpAndDeptById(1);
        emp.getDept().getDeptName();
        System.out.println(emp);
    }
}