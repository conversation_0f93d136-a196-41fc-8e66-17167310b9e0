//package com.sam;
//
//import com.github.pagehelper.Page;
//import com.github.pagehelper.PageHelper;
//import com.github.pagehelper.PageInfo;
//import com.sam.entity.SC;
//import com.sam.entity.Student;
//import org.apache.ibatis.io.Resources;
//import org.apache.ibatis.session.SqlSession;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.apache.ibatis.session.SqlSessionFactoryBuilder;
//import org.junit.After;
//import org.junit.Before;
//import org.junit.Test;
//
//import java.util.List;
//
///**
// * Unit test for simple App.
// */
//public class AppTest {
//
//    private SqlSession sqlSession;
//    private StudentMapper studentMapper;
//    private SCMapper SCMapper;
//
//    @Before
//    public void setUp() throws Exception {
//        System.out.println("----开始测试----");
//        System.out.println("1.加载mybatis-config.xml和UserMapper.xml文件");
//        String resource = "mybatis-config.xml";
//        System.out.println("2.构建SqlSessionFactory(管理数据库会话的核心对象)");
//        SqlSessionFactory sqlSessionFactory =
//                new SqlSessionFactoryBuilder().build(Resources.getResourceAsStream(resource));
//        System.out.println("3.从SqlSessionFactory中打开一个SqlSession");
//        sqlSession = sqlSessionFactory.openSession();
//        System.out.println("4.使用SqlSession创建StudentMapper接口的代理对象");
//        studentMapper = sqlSession.getMapper(StudentMapper.class);
//    }
//
//    @After
//    public void tearDown() throws Exception {
//        System.out.println("6.调用 SqlSession.commit() 手动提交事务");
//        sqlSession.commit();
//        System.out.println("7.调用 SqlSession.close() 释放连接（归还连接池）");
//        sqlSession.close();
//        System.out.println("----结束测试----");
//    }
//
//    @Test
//    public void m1() {
//        System.out.println("5.调用Mapper接口中的方法");
//        Student student = studentMapper.findStudentInfo(1);
//        System.out.println(student);
//        List<SC> scList = student.getScList();
//        for (SC sc : scList) {
//            System.out.println("\t" + sc.getCourse().getCname()+"\t"+sc.getScore());
//        }
//    }
//
//    @Test
//    public void m2() {
//        System.out.println("5.调用Mapper接口中的方法");
//        Student student = studentMapper.findStudentInfo(1);
//        System.out.println(student);
//        List<SC> scList = student.getScList();
//        for (SC sc : scList) {
//            System.out.println("\t" + sc.getCourse().getCname()+"\t"+sc.getScore());
//        }
//    }
//
//    @Test
//    public void m3() {
//        System.out.println("5.调用Mapper接口中的方法");
//        //分页
//        Page<Object> page = PageHelper.startPage(2, 3);
//        //分页查询
//        List<Student> students = studentMapper.findStudentByPage();
//        System.out.println("总记录数：" + page.getTotal());
//        System.out.println("总页数：" + page.getPages());
//        System.out.println("当前页：" + page.getPageNum());
//        System.out.println("每页记录数：" + page.getPageSize());
//        System.out.println("当前页显示的数据:");
//        for (Student student : students) {
//            System.out.println(student.getSid());
//            System.out.println(student.getSname());
//        }
//    }
//
//    @Test
//    public void m4() {
//        System.out.println("5.调用Mapper接口中的方法");
//        // 1.在需要分页的查询前调用,设置分页参数
//        PageHelper.startPage(2, 3);
//        // 2.调用需要分页查询的方法
//        List<Student> list = studentMapper.findStudentByPage();
//        // 3. 获取分页信息
//        PageInfo<Student> pageInfo=new PageInfo<>(list);
//        System.out.println("总记录数：" + pageInfo.getTotal());
//        System.out.println("总页数：" + pageInfo.getPages());
//        System.out.println("当前页：" + pageInfo.getPageNum());
//        System.out.println("每页记录数：" + pageInfo.getPageSize());
//        System.out.println("当前页显示的数据:");
//        for (Student student : list) {
//            System.out.println(student.getSid()+"\t"+ student.getSname());
//        }
//    }
//}