package com.sam;

import com.sam.entity.Student;
import com.sam.mapper.AllMapper;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * AllMapper测试类
 */
public class AllMapperTest {

    private SqlSession sqlSession;
    private AllMapper allMapper;

    @Before
    public void setUp() throws IOException {
        System.out.println("----开始测试----");
        System.out.println("1.读取mybatis-config.xml配置文件");
        InputStream inputStream = Resources.getResourceAsStream("mybatis-config.xml");
        System.out.println("2.构建SqlSessionFactory");
        SqlSessionFactory sqlSessionFactory = new SqlSessionFactoryBuilder().build(inputStream);
        System.out.println("3.获取SqlSession");
        sqlSession = sqlSessionFactory.openSession();
        System.out.println("4.获取Mapper接口的代理对象");
        allMapper = sqlSession.getMapper(AllMapper.class);
    }

    @After
    public void tearDown() {
        System.out.println("6.调用 SqlSession.commit() 手动提交事务");
        sqlSession.commit();
        System.out.println("7.调用 SqlSession.close() 释放连接（归还连接池）");
        sqlSession.close();
        System.out.println("----结束测试----");
    }

    @Test
    public void testFindAllWithStuinfo() {
        System.out.println("测试需求一：查询所有学生和所有学生信息");
        List<Student> students = allMapper.findAllWithStuinfo();
        for (Student student : students) {
            System.out.println("学生ID: " + student.getSid() + ", 姓名: " + student.getSname());
            if (student.getStuinfo() != null) {
                System.out.println("  年龄: " + student.getStuinfo().getSage() + 
                                 ", 性别: " + student.getStuinfo().getSsex() + 
                                 ", 电话: " + student.getStuinfo().getStel());
            }
        }
    }

    @Test
    public void testFindByIdWithScore() {
        System.out.println("测试需求二：查询指定学生信息，和学生分数");
        Student student = allMapper.findByIdWithScore(1);
        System.out.println("学生信息: " + student.getSname());
        if (student.getScList() != null) {
            student.getScList().forEach(sc -> {
                System.out.println("  课程ID: " + sc.getC_id() + ", 分数: " + sc.getScore());
            });
        }
    }

    @Test
    public void testFindAllScoreWithCourse() {
        System.out.println("测试需求三：查询所有分数和分数对应的课程");
        List<Map<String, Object>> results = allMapper.findAllScoreWithCourse();
        for (Map<String, Object> result : results) {
            System.out.println("课程: " + result.get("cname") + ", 分数: " + result.get("score"));
        }
    }

    @Test
    public void testFindAllWithScoreAndCourse() {
        System.out.println("测试需求四：查询全部学生的分数和对应的课程");
        List<Student> students = allMapper.findAllWithScoreAndCourse();
        for (Student student : students) {
            System.out.println("学生: " + student.getSname());
            if (student.getCourses() != null) {
                student.getCourses().forEach(course -> {
                    System.out.println("  课程: " + course.getCname() + ", 分数: " + course.getScore());
                });
            }
        }
    }

    @Test
    public void testFindById() {
        System.out.println("测试需求五：查询指定学生的详细信息和课程信息");
        Student student = allMapper.findById(1);
        System.out.println("学生信息: " + student.getSname());
        
        if (student.getStuinfo() != null) {
            System.out.println("详细信息 - 年龄: " + student.getStuinfo().getSage() + 
                             ", 性别: " + student.getStuinfo().getSsex() + 
                             ", 电话: " + student.getStuinfo().getStel());
        }
        
        if (student.getCourses() != null) {
            System.out.println("课程信息:");
            student.getCourses().forEach(course -> {
                System.out.println("  课程: " + course.getCname() + ", 分数: " + course.getScore());
            });
        }
    }
}
