<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sam.mapper.AllMapper">


    <resultMap id="StudentFullMap" type="Student">
        <id property="sid" column="sid"/>
        <result property="sname" column="sname"/>

        <!-- 一对一：学生详情 -->
        <association property="stuinfo" javaType="Stuinfo">
            <result property="sage" column="sage"/>
            <result property="ssex" column="ssex"/>
            <result property="stel" column="stel"/>
        </association>

        <!-- 一对多：课程列表 -->
        <collection property="courses" ofType="Course">
            <id property="cid" column="cid"/>
            <result property="cname" column="cname"/>
            <result property="score" column="score"/>
        </collection>
    </resultMap>



    <!--    需求一：查询所有学生和所有学生信息-->
    <select id="findAllWithStuinfo" resultType="com.sam.entity.Student">
        

    </select>

    <!--    需求二：查询指定学生信息，和学生分数-->
    <select id="findByIdWithScore" resultType="com.sam.entity.Student">

    </select>

    <!--需求三：查询所有分数和分数对应的课程-->
    <select id="findAllScoreWithCourse" resultType="java.util.Map">

    </select>

    <!--需求四，查询全部学生的分数和对应的课程-->
    <select id="findAllWithScoreAndCourse" resultType="com.sam.entity.Student">

    </select>

    <!--需求五，根据id查询学生信息和课程信息-->
    <select id="findById" resultType="com.sam.entity.Student">

    </select>


</mapper>