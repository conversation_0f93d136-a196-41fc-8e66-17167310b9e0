<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sam.mapper.AllMapper">


    <resultMap id="StudentFullMap" type="Student">
        <id property="sid" column="sid"/>
        <result property="sname" column="sname"/>

        <!-- 一对一：学生详情 -->
        <association property="stuinfo" javaType="Stuinfo">
            <result property="sage" column="sage"/>
            <result property="ssex" column="ssex"/>
            <result property="stel" column="stel"/>
        </association>
        

        <!-- 一对多：课程列表 -->
        <collection property="courses" ofType="Course">
            <id property="cid" column="cid"/>
            <result property="cname" column="cname"/>
            <result property="score" column="score"/>
        </collection>
        
    </resultMap>


    <!-- 需求一：查询所有学生和所有学生信息 -->
    <resultMap id="StudentWithStuinfoMap" type="Student">
        <id property="sid" column="sid"/>
        <result property="sname" column="sname"/>
        <association property="stuinfo" javaType="Stuinfo">
            <result property="sage" column="sage"/>
            <result property="ssex" column="ssex"/>
            <result property="stel" column="stel"/>
        </association>
    </resultMap>

    <select id="findAllWithStuinfo" resultMap="StudentWithStuinfoMap">
        SELECT s.sid, s.sname, si.sage, si.ssex, si.stel
        FROM student s
        LEFT JOIN stuinfo si ON s.sid = si.sid
    </select>

    <!-- 需求二：查询指定学生信息，和学生分数 -->
    <resultMap id="StudentWithScoreMap" type="Student">
        <id property="sid" column="sid"/>
        <result property="sname" column="sname"/>
        <collection property="scList" ofType="SC">
            <result property="s_id" column="s_id"/>
            <result property="c_id" column="c_id"/>
            <result property="score" column="score"/>
        </collection>
    </resultMap>

    <select id="findByIdWithScore" resultMap="StudentWithScoreMap">
        SELECT s.sid, s.sname, sc.s_id, sc.c_id, sc.score
        FROM student s
        LEFT JOIN sc ON s.sid = sc.s_id
        WHERE s.sid = #{sid}
    </select>

    <!-- 需求三：查询所有分数和分数对应的课程 -->
    <select id="findAllScoreWithCourse" resultType="java.util.Map">
        SELECT sc.score, c.cid, c.cname
        FROM sc
        INNER JOIN course c ON sc.c_id = c.cid
        ORDER BY sc.score DESC
    </select>

    <!-- 需求四：查询全部学生的分数和对应的课程 -->
    <resultMap id="StudentWithScoreAndCourseMap" type="Student">
        <id property="sid" column="sid"/>
        <result property="sname" column="sname"/>
        <collection property="courses" ofType="Course">
            <id property="cid" column="cid"/>
            <result property="cname" column="cname"/>
            <result property="score" column="score"/>
        </collection>
    </resultMap>

    <select id="findAllWithScoreAndCourse" resultMap="StudentWithScoreAndCourseMap">
        SELECT s.sid, s.sname, c.cid, c.cname, sc.score
        FROM student s
        LEFT JOIN sc ON s.sid = sc.s_id
        LEFT JOIN course c ON sc.c_id = c.cid
        ORDER BY s.sid
    </select>

    <!-- 需求五：查询指定学生的详细信息和课程信息 -->
    <select id="findById" resultMap="StudentFullMap">
        SELECT s.sid, s.sname, si.sage, si.ssex, si.stel, c.cid, c.cname, sc.score
        FROM student s
        LEFT JOIN stuinfo si ON s.sid = si.sid
        LEFT JOIN sc ON s.sid = sc.s_id
        LEFT JOIN course c ON sc.c_id = c.cid
        WHERE s.sid = #{sid}
    </select>


</mapper>