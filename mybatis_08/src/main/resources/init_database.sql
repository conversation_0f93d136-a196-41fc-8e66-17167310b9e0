-- 创建数据库表和插入测试数据

-- 创建学生表
CREATE TABLE IF NOT EXISTS student (
    sid INT PRIMARY KEY AUTO_INCREMENT,
    sname VARCHAR(50) NOT NULL
);

-- 创建学生详情表
CREATE TABLE IF NOT EXISTS stuinfo (
    sid INT PRIMARY KEY,
    sage INT,
    ssex VARCHAR(10),
    stel VARCHAR(20),
    FOREIGN KEY (sid) REFERENCES student(sid)
);

-- 创建课程表
CREATE TABLE IF NOT EXISTS course (
    cid INT PRIMARY KEY AUTO_INCREMENT,
    cname VARCHAR(100) NOT NULL
);

-- 创建选课表（学生-课程关系表）
CREATE TABLE IF NOT EXISTS sc (
    s_id INT,
    c_id INT,
    score DECIMAL(5,2),
    PRIMARY KEY (s_id, c_id),
    FOREIGN KEY (s_id) REFERENCES student(sid),
    FOR<PERSON><PERSON><PERSON> KEY (c_id) REFERENCES course(cid)
);

-- 插入测试数据

-- 插入学生数据
INSERT INTO student (sname) VALUES 
('张三'),
('李四'),
('王五'),
('赵六');

-- 插入学生详情数据
INSERT INTO stuinfo (sid, sage, ssex, stel) VALUES 
(1, 20, '男', '13800138001'),
(2, 21, '女', '13800138002'),
(3, 19, '男', '13800138003'),
(4, 22, '女', '13800138004');

-- 插入课程数据
INSERT INTO course (cname) VALUES 
('Java程序设计'),
('数据库原理'),
('Web开发技术'),
('数据结构与算法');

-- 插入选课数据
INSERT INTO sc (s_id, c_id, score) VALUES 
(1, 1, 85.5),
(1, 2, 92.0),
(1, 3, 78.5),
(2, 1, 88.0),
(2, 2, 95.5),
(2, 4, 82.0),
(3, 1, 76.5),
(3, 3, 89.0),
(3, 4, 91.5),
(4, 2, 87.5),
(4, 3, 93.0),
(4, 4, 85.0);
