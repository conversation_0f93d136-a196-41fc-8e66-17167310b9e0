package com.sam.mapper;

import com.sam.entity.Student;

import java.util.List;
import java.util.Map;

public interface AllMapper {

    //需求一：查询所有学生和所有学生信息
    List<Student> findAllWithStuinfo();

    //需求二：查询指定学生信息，和学生分数
    Student findByIdWithScore(Integer sid);

    //需求三：查询所有分数和分数对应的课程
    List<Map<String,Object>> findAllScoreWithCourse();

    //需求四，查询全部学生的分数和对应的课程
    List<Student> findAllWithScoreAndCourse();

    //需求五，查询指定学生的详细信息和课程信息
    Student findById(Integer sid);

}
