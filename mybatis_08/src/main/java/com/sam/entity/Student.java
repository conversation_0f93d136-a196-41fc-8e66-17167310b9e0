package com.sam.entity;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-22 10:41
 */
@Data
@ToString(exclude = {"scList", "courses"})
public class Student {
    private Integer sid;
    private String sname;

    //配置一对多关系,一个学生有多条选课记录
    private List<SC> scList;

    //配置一对一关系，学生详情信息
    private Stuinfo stuinfo;

    //配置一对多关系，学生的课程列表（带分数）
    private List<Course> courses;

}
