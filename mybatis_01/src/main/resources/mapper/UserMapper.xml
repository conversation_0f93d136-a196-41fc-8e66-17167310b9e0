<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.mybatis.example.UserMapper">

    <sql id="fields">
        id,username,age,birthday,sex,address,status
    </sql>



<!--参数类型为hashmap map.put-->
    <select id="selectUserByMap" parameterType="hashmap" resultType="user">
        select * from tb_user where username like concat('%',#{k1},'%') or age=#{k2}
    </select>
    
<!--根据用户名和年龄查询-->
    <select id="selectUserByNameAndAge" parameterType="queryVo" resultType="user">
        select * from tb_user where username like concat('%',#{user.username},'%') or age =#{user.age}
    </select>

<!--原始类型传入使用_int，数据类型传入使用integer-->

    <select id="selectUser" resultType="user">
        select <include refid="fields"> </include> from tb_user where id=#{id}
    </select>



    <insert id="insertUser" parameterType="user">

        insert into
        tb_user(username,age,birthday,sex,address,status)
        values(#{username},#{age},#{birthday},#{sex},#{address},#{status})

    </insert>

    <insert id="insertUser2" parameterType="user" useGeneratedKeys="true" keyProperty="id">

        insert into
        tb_user
        values(#{id},#{username},#{age},#{birthday},#{sex},#{address},#{status})

    </insert>

    <update id="updateUser" parameterType="user">

        update tb_user
        set username=#{username},age=#{age},birthday=#{birthday},sex=#{sex},address=#{address}
        where id=#{id}

    </update>

    <delete id="deleteUser" parameterType="int">
        delete from tb_user where id=#{id}
    </delete>

    <select id="userList" resultType="user">
        select * from tb_user
    </select>

</mapper>