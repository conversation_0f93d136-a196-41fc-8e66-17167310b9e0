<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sam.mapper.UserMapper2">

    <resultMap id="rm1" type="user">
        <id property="userId" column="id"/>
        <result property="userName" column="username"/>
        <result property="userAge" column="age"/>
        <result property="userBirthday" column="birthday"/>
        <result property="userSex" column="sex"/>
        <result property="userAddress" column="address"/>
        <result property="userStatus" column="status"/>
    </resultMap>

    <select id="selectUser" parameterType="_int" resultMap="rm1">
        select * from tb_user where id=#{id}
    </select>


</mapper>