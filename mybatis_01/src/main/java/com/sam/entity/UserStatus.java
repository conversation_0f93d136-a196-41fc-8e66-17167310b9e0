package com.sam.entity;

public enum UserStatus {
    NORMAL("正常"),
    LOCKED("封禁");

    private final String desc;

    UserStatus(String desc){
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    // 根据数据库值获取枚举值
    public static UserStatus getByDesc(String desc) {
        for (UserStatus status : UserStatus.values()) {
            if (status.getDesc().equals(desc)) {
                return status;
            }
        }
        throw  new IllegalArgumentException("未知的枚举值:" + desc);
    }

}
