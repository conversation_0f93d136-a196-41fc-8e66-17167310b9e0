package com.sam.typehandler;

import com.sam.entity.UserStatus;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-20 09:25
 * 自定义类型转换器
 *   T ---> java类型
 * enum  ---->  com.sam.entity.UserStatus
 */


public class EnumTypeHandler extends BaseTypeHandler<UserStatus> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, UserStatus parameter, JdbcType jdbcType) throws SQLException {

        ps.setString(i,parameter.getDesc());

    }

    @Override
    public UserStatus getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String desc = rs.getString(columnName);
        if(desc == null){
            return null;
        }else{
            UserStatus status = UserStatus.getByDesc(desc);
            return status;
        }
    }

    @Override
    public UserStatus getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String desc = rs.getString(columnIndex);
        if(desc == null){
            return null;
        }else{
            UserStatus status = UserStatus.getByDesc(desc);
            return status;
        }
    }

    @Override
    public UserStatus getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String desc = cs.getString(columnIndex);
        if(desc == null){
            return null;
        }else{
            UserStatus status = UserStatus.getByDesc(desc);
            return status;
        }

    }
}