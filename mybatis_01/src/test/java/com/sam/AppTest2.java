package com.sam;


import com.sam.entity.User;
import com.sam.entity.User;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.junit.Before;
import org.junit.Test;

import java.io.InputStream;

public class AppTest2 {

    private SqlSessionFactory sqlSessionFactory;

    @Before
    public void before() throws Exception {
        System.out.println("before方法");
        //1.加载配置文件 (mybatis-config.xml,UserMapper.xml)
        String resource = "mybatis-config.xml";
        //2.创建SqlSessionFactory对象
        InputStream inputStream = Resources.getResourceAsStream(resource);
        sqlSessionFactory =
                new SqlSessionFactoryBuilder().build(inputStream);
    }

    @Test
    public  void testSelectUser() throws Exception {
        SqlSession sqlSession = sqlSessionFactory.openSession();
        User user = sqlSession.selectOne("com.sam.mapper.UserMapper2.selectUser", 1);
        System.out.println(user);
        sqlSession.close();
    }

}
