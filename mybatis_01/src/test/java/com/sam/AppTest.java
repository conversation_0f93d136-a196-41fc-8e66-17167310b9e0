//package com.sam;
//
//import com.sam.entity.User;
//import com.sam.entity.UserStatus;
//import com.sam.vo.QueryVo;
//import junit.framework.TestCase;
//import junit.framework.TestSuite;
//import org.apache.ibatis.io.Resources;
//import org.apache.ibatis.session.SqlSession;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.apache.ibatis.session.SqlSessionFactoryBuilder;
//import org.junit.Before;
//import org.junit.Test;
//
//import java.io.IOException;
//import java.io.InputStream;
//import java.sql.Connection;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.List;
//
///**
// * Unit test for simple App.
// */
//public class AppTest {
//
//    private SqlSessionFactory sqlSessionFactory;
//
//    @Before
//    public void before() throws Exception {
//        System.out.println("before方法");
//        //1.加载配置文件 (mybatis-config.xml,UserMapper.xml)
//        String resource = "mybatis-config.xml";
//        //2.创建SqlSessionFactory对象
//        InputStream inputStream = Resources.getResourceAsStream(resource);
//        sqlSessionFactory =
//                new SqlSessionFactoryBuilder().build(inputStream);
//    }
//
//
//    @Test
//    public void testSelectUser() throws Exception {
//        //3.通过SqlSessionFactory创建SqlSession对象
//        SqlSession sqlSession = sqlSessionFactory.openSession();
//        //4.调用SqlSession的API方法执行sql语句
//        User user = sqlSession.selectOne("org.mybatis.example.UserMapper.selectUser", 1);
//        System.out.println(user);
//        //5.关闭SqlSession
//        sqlSession.close();
//    }
//
//    @Test
//    public void insertUser() throws Exception {
//        SqlSession sqlSession = sqlSessionFactory.openSession();
//        User user = new User();
//        user.setUsername("老王");
//        user.setAge(18);
//        user.setBirthday(new Date());
//        user.setSex("男");
//        user.setAddress("湖北");
//        user.setStatus(UserStatus.LOCKED);
//        int count = sqlSession.insert("org.mybatis.example.UserMapper.insertUser", user);
//        System.out.println(count);
//        //默认手动提交事务
//        sqlSession.commit();
//        //释放连接资源
//        sqlSession.close();
//    }
//
//    @Test
//    public void insertUser2() throws Exception {
//        SqlSession sqlSession = sqlSessionFactory.openSession();
//        User user = new User();
//        user.setId(0);
//        user.setUsername("老八");
//        user.setAge(18);
//        user.setBirthday(new Date());
//        user.setSex("女");
//        user.setAddress("武汉");
//        user.setStatus(UserStatus.NORMAL);
//        int n = sqlSession.insert("org.mybatis.example.UserMapper.insertUser2", user);
//        System.out.println("是否插入成功:"+n);
//        //默认手动提交事务
//        sqlSession.commit();
//        System.out.println(user.getId());
//        //释放连接资源
//        sqlSession.close();
//    }
//
//    @Test
//    public void testUpdateUser() throws Exception {
//        //3.通过SqlSessionFactory创建SqlSession对象
//        SqlSession sqlSession = sqlSessionFactory.openSession();
//        //4.调用SqlSession的API方法执行sql语句
//        User user = new User();
//        user.setId(9);//主键自动递增
//        user.setUsername("小王吧");
//        user.setAge(20);
//        user.setBirthday(new Date());
//        user.setSex("男");
//        user.setAddress("上海");
//        int n = sqlSession.update("org.mybatis.example.UserMapper.updateUser", user);
//        System.out.println("n = " + n);
//        sqlSession.commit(); //一定不要忘记提交事务
//        sqlSession.close();
//    }
//
//    @Test
//    public void testDeleteUser() throws IOException {
//        SqlSession sqlSession = sqlSessionFactory.openSession();
//        int n = sqlSession.delete("org.mybatis.example.UserMapper.deleteUser", 8);
//        System.out.println("n = " + n);
//        sqlSession.commit();
//        sqlSession.close();
//    }
//
//    @Test
//    public void testUserList() throws IOException {
//        SqlSession sqlSession = sqlSessionFactory.openSession();
//        List<User> userList = sqlSession.selectList("org.mybatis.example.UserMapper.userList");
//        for (User user : userList) {
//            System.out.println(user);
//        }
//        sqlSession.close();
//    }
//
//    @Test
//    public void getDBInfo() throws Exception{
//        InputStream stream = AppTest.class.getClassLoader().getResourceAsStream("mybatis-config.xml");
//        SqlSessionFactory factory = new SqlSessionFactoryBuilder().build(stream);
//        SqlSession sqlSession = factory.openSession();
//        Connection connection = sqlSession.getConnection();
//        String dbName = connection.getMetaData().getDatabaseProductName();
//        String dbVersion = connection.getMetaData().getDatabaseProductVersion();
//        System.out.println("数据库名称是：" + dbName + "；版本是：" + dbVersion);
//    }
//
//    @Test
//    public void testSelectUserByNameAndAge() throws Exception {
//        //3.通过SqlSessionFactory创建SqlSession对象
//        SqlSession sqlSession = sqlSessionFactory.openSession();
//        QueryVo queryVo=new QueryVo();
//        User user=new User();  user.setUsername("小"); user.setAge(18);
//        queryVo.setUser(user);
//        //4.调用SqlSession的API方法执行sql语句
//        List<User> list = sqlSession.selectList("org.mybatis.example.UserMapper.selectUserByNameAndAge", queryVo);
//        list.forEach(System.out::println);
//        //5.关闭SqlSession
//        sqlSession.close();
//    }
//
//    @Test
//    public void testSelectUserByMap(){
//        //3.通过SqlSessionFactory创建SqlSession对象
//        SqlSession sqlSession = sqlSessionFactory.openSession();
//        HashMap<String,Object> map =new HashMap<>();
//        map.put("k1","小");
//        map.put("k2",18);
//        List<User> list=  sqlSession.selectList("org.mybatis.example.UserMapper.selectUserByMap",map);
//        list.forEach(System.out::println);
//        sqlSession.close();
//    }
//
//
//}