<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sam.mapper.BookMapper">

    <update id="updateBook" parameterType="book">
        update tb_book
        <set>
                <if test="title != null and title != ''">
                    title = #{title}
                </if>
                <if test="author != null and author != ''">
                    author = #{author}
                </if>
                <if test="price != null and price != ''">
                    price = #{price}
                </if>
                <if test="sales != null and sales != ''">
                    sales = #{sales}
                </if>
                <if test="stock != null and stock != ''">
                    stock = #{stock}
                </if>
                <if test="img != null and img != ''">
                    img = #{img}
                </if>
        </set>

        where id = #{id}

    </update>

<!--    <select id="selectBook" parameterType="book" resultType="book">-->
<!--        select * from tb_book-->
<!--        <where>-->
<!--&lt;!&ndash;当使用<where>到时候&ndash;&gt;-->
<!--        <if test="title != null and title != ''">-->
<!--            and title =#{title}-->
<!--        </if>-->
<!--        <if test="author != null and author != ''">-->
<!--            and author = #{author}-->
<!--        </if>-->
<!--        </where>-->
<!--    </select>-->

    <select id="selectBookByChoose" parameterType="book" resultType="com.sam.entity.Book">

        select * from tb_book
        <where>
                <choose>
                        <when test="title != null and title != ''">
                                title = #{title}
                        </when>
                        <when test="author != null and author != ''">
                                author = #{author}
                        </when>
                        <otherwise>
                                1=1
                        </otherwise>
                </choose>
        </where>

    </select>

    <select id="selectBookByForeach" parameterType="bookVO" resultType="com.sam.entity.Book">

        select * from tb_book
        <where>
                <foreach collection="IdList" item="num" open="id in (" close=")" separator=",">
                        #{num}
                </foreach>
        </where>


    </select>

    <select id="selectBookByBind" parameterType="string" resultType="book">

        <bind name="pattern" value="'%'+key+'%'" />
        select * from tb_book where title like #{pattern}
    </select>


</mapper>