package com.sam.mapper;

import com.sam.entity.Book;
import com.sam.sql.BookSqlProvider;
import com.sam.vo.BookVO;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

public interface BookMapper {
    //查询所有图书
    @Select({
            "<script>",
            "select * from tb_book",
            "<where>",
            "   <if test='title != null '> and title=#{title} </if>  ",
            "   <if test='author != null'> and author= #{author} </if>  ",
            "</where>",
            "</script>"
    })
//    @SelectProvider(type = BookSqlProvider.class,method = "sql1")
    List<Book> selectBook(Book book);

    //更新图书信息
    int updateBook(Book book);

    //根据选择的条件查询图书信息
    List<Book> selectBookByChoose(Book book);

    //根据id集合查询图书信息
    List<Book> selectBookByForeach(BookVO bookVO);

    List<Book> selectBookByBind(String key);
}
