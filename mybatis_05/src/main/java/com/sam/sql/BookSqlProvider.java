package com.sam.sql;

import com.sam.entity.Book;
import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-21 13:51
 */
public class BookSqlProvider {
    public String sql(Book book) {
        return new SQL(){{
            SELECT("*");
            FROM("tb_book");
            if (book.getTitle() != null && !book.getTitle().equals("")){
                WHERE("title = #{title}");
            }
            if (book.getAuthor() != null && !book.getAuthor().equals("")){
                WHERE("author = #{author}");
            }
        }}
            .toString();
    }
}

