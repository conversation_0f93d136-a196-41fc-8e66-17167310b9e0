package com.sam.mapper;

import com.sam.entity.Book;
import com.sam.vo.BookVO;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

import static org.junit.Assert.*;

public class BookMapperTest {

    private SqlSession sqlSession;
    private BookMapper bookMapper;

    @Before
    public void setUp() throws Exception {
        System.out.println("----开始测试----");
        System.out.println("1.加载mybatis-config.xml和UserMapper.xml文件");
        String resource = "mybatis-config.xml";
        System.out.println("2.构建SqlSessionFactory(管理数据库会话的核心对象)");
        SqlSessionFactory sqlSessionFactory =
                new SqlSessionFactoryBuilder().build(Resources.getResourceAsStream(resource));
        System.out.println("3.从SqlSessionFactory中打开一个SqlSession");
        sqlSession = sqlSessionFactory.openSession();
        System.out.println("4.使用SqlSession创建BookMapper接口的代理对象");
        bookMapper = sqlSession.getMapper(BookMapper.class);
    }

    @After
    public void tearDown() throws Exception {
        System.out.println("6.调用 SqlSession.commit() 手动提交事务");
        sqlSession.commit();
        System.out.println("7.调用 SqlSession.close() 释放连接（归还连接池）");
        sqlSession.close();
        System.out.println("----结束测试----");
    }

    @Test
    public void selectBook() {
        Book book = new Book();
        book.setTitle("边城");
        book.setAuthor("沈从文");
        List<Book> books = bookMapper.selectBook(book);
        books.forEach(System.out::println);
    }

    @Test
    public void updateBook() {
        Book book = new Book();
        book.setId(1);
        book.setAuthor("雨姐");
        int n = bookMapper.updateBook(book);
        System.out.println(n>0?"修改成功":"修改失败");
    }

    @Test
    public void selectBookByChoose() {
        Book book = new Book();
        book.setTitle("边城");
        List<Book> books = bookMapper.selectBookByChoose(book);
        books.forEach(System.out::println);
    }

    @Test
    public void selectBookByForeach() {
        BookVO bookVO = new BookVO();
        bookVO.setIdList(List.of(1,2,3));
        //bookVO.setIdList(Arrays.asList(1, 2, 4, 5));
        List<Book> books = bookMapper.selectBookByForeach(bookVO);
        books.forEach(System.out::println);
    }

    @Test
    public void selectBookByBind() {
        List<Book> books = bookMapper.selectBookByBind("边");
        books.forEach(System.out::println);
    }
}