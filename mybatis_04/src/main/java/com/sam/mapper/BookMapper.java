package com.sam.mapper;

import com.sam.entity.Book;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.util.List;

public interface BookMapper {

    //添加Book数据
    //这是使用Mapper.xml文件的方法
    //int insertBook(Book book);
    @Insert("insert into tb_book values (0,#{title},#{author},#{price},#{sales},#{stock},#{img})")
    @Options(useGeneratedKeys = true,keyProperty = "id")
    int insertBook(Book book);

    //查询Book数据
    // ID是给这个结果映射起一个名字，方便复用
    @Results(id="bookMap",value = {
            //主键映射。id只能有一个
            @Result(id = true,property = "id",column = "id"),
            @Result(property = "title",column = "title"),
            @Result(property = "author",column = "author"),
            @Result(property = "price",column = "price"),
            @Result(property = "sales",column = "sales"),
            @Result(property = "stock",column = "stock"),
            @Result(property = "img",column = "img")
    })
    @Select("select * from tb_book")
    @Options(useGeneratedKeys = true,keyProperty = "id")
    List<Book> bookList();

    //根据图书id修改价格和库存
    @Update("update tb_book set price=#{price},stock=#{stock} where id=#{id}")
    @Options(useGeneratedKeys = true,keyProperty = "id")
    //如果无法识别到类名，则使用Param进行参数命名绑定
//    int updateUserPriceAndStock(@Param("price") BigDecimal price,
//                                @Param("stock") Integer stock,
//                                @Param("id") Integer id);
    int updateUserPriceAndStock(Book book);

    //删除Book数据
    @Delete("delete from tb_book where id=#{id}")
    @Options(useGeneratedKeys = true,keyProperty = "id")
    int deleteBook(Integer id);

}
