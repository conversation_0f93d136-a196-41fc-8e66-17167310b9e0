package com.sam.mapper;

import com.sam.entity.Book;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.Assert.*;

public class BookMapperTest {

    private SqlSession sqlSession;
    private BookMapper bookMapper;

    @Before
    public void setUp() throws Exception {
        System.out.println("----开始测试----");
        System.out.println("1.加载mybatis-config.xml和UserMapper.xml文件");
        String resource = "mybatis-config.xml";
        System.out.println("2.构建SqlSessionFactory(管理数据库会话的核心对象)");
        SqlSessionFactory sqlSessionFactory =
                new SqlSessionFactoryBuilder().build(Resources.getResourceAsStream(resource));
        System.out.println("3.从SqlSessionFactory中打开一个SqlSession");
        sqlSession = sqlSessionFactory.openSession();
        System.out.println("4.使用SqlSession创建BookMapper接口的代理对象");
        bookMapper = sqlSession.getMapper(BookMapper.class);
    }

    @After
    public void tearDown() throws Exception {
        System.out.println("6.调用 SqlSession.commit() 手动提交事务");
        sqlSession.commit();
        System.out.println("7.调用 SqlSession.close() 释放连接（归还连接池）");
        sqlSession.close();
        System.out.println("----结束测试----");
    }

    @Test
    public void insertBook() {
        System.out.println("5.调用BookMapper接口方法");
        Book book = new Book();
        book.setTitle("东北玉姐");
        book.setAuthor("老蒯");
        book.setPrice(BigDecimal.valueOf(9.99));
        book.setSales(100);
        book.setStock(100);
        book.setImg("雨姐.jpg");
        int n = bookMapper.insertBook(book);
        System.out.println(n>0?"插入成功":"插入失败");
    }

    @Test
    public void bookList() {
        List<Book> books = bookMapper.bookList();
        books.forEach(System.out::println);
    }

    @Test
    public void updateUserPriceAndStock() {
        Book book = new Book();
        book.setId(34);
        book.setPrice(BigDecimal.valueOf(111.11));
        book.setStock(666);
        int n = bookMapper.updateUserPriceAndStock(book);
        System.out.println(n>0?"修改成功":"修改失败");
    }

    @Test
    public void deleteBook() {
        int n = bookMapper.deleteBook(34);
        System.out.println(n>0?"删除成功":"删除失败");
    }
}