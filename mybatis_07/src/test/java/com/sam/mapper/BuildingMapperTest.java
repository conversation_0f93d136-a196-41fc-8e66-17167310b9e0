package com.sam.mapper;

import com.sam.entity.Building;
import com.sam.entity.Community;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

public class BuildingMapperTest {

    private SqlSession sqlSession;
    private BuildingMapper buildingMapper;
    private CommunityMapper communityMapper;

    @Before
    public void setUp() throws Exception {
        System.out.println("----开始测试----");
        System.out.println("1.加载mybatis-config.xml和UserMapper.xml文件");
        String resource = "mybatis-config.xml";
        System.out.println("2.构建SqlSessionFactory(管理数据库会话的核心对象)");
        SqlSessionFactory sqlSessionFactory =
                new SqlSessionFactoryBuilder().build(Resources.getResourceAsStream(resource));
        System.out.println("3.从SqlSessionFactory中打开一个SqlSession");
        sqlSession = sqlSessionFactory.openSession();
        System.out.println("4.使用SqlSession创建EmpMapper/DeptMapper接口的代理对象");
        buildingMapper = sqlSession.getMapper(BuildingMapper.class);
        communityMapper=sqlSession.getMapper(CommunityMapper.class);
    }

    @After
    public void tearDown() throws Exception {
        System.out.println("6.调用 SqlSession.commit() 手动提交事务");
        sqlSession.commit();
        System.out.println("7.调用 SqlSession.close() 释放连接（归还连接池）");
        sqlSession.close();
        System.out.println("----结束测试----");
    }

    @Test
    public void selectCommunityAndBuilding() {
        System.out.println("5.调用CommunityMapper接口中的方法，执行selectCommunityAndBuilding查询操作");
        Community community = communityMapper.selectCommunityAndBuilding(1);
        System.out.println(community);
    }

    @Test
    public void updateCommunity() {
        System.out.println("5.调用CommunityMapper接口中的方法，执行updateCommunity查询操作");
        Community community = new Community();
        community.setCid(1);
        community.setCname("测试更改新校区");
        community.setCpic("测试更改新校区.jpg");
        community.setCaddress("测试更改新校区地址");
        int n = communityMapper.updateCommunity(community);
        System.out.println(n>0?"修改成功":"修改失败");
    }

    @Test
    public void selectBuildingByDid() {
        System.out.println("5.调用CommunityMapper接口中的方法，执行selectBuildingByDid查询操作");
        Building building = buildingMapper.selectBuildingByDid("1栋");
        System.out.println(building);
        System.out.println(building.getCommunity());
    }
}
