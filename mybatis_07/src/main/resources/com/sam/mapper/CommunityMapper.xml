<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sam.mapper.CommunityMapper">

    <!--    需求1： 根据小区ID查询小区信息及对应楼栋信息（分步骤查询）-->
    <resultMap id="communityBuildingMap" type="com.sam.entity.Community">
            <id property="cid" column="cid"/>
            <result property="cpic" column="cpic"/>
            <result property="caddress" column="caddress"/>
            <result property="cname" column="cname"/>
    <!--collection分步查询-->
    <collection property="buildingList"
                column="cid"
                javaType="list"
                ofType="com.sam.entity.Building"
                select="com.sam.mapper.BuildingMapper.selectBuildingByCid"/>
    </resultMap>

    <select id="selectCommunityAndBuilding" resultMap="communityBuildingMap">
        select * from community where cid=#{cid}
    </select>

    <!--    需求2： 使用动态SQL对小区信息进行更改-->
    <update id="updateCommunity">
        update community
        <set>
            <if test="cname != null and cname != ''">
                cname = #{cname},
            </if>
            <if test="cpic != null and cpic != ''">
                cpic = #{cpic},
            </if>
            <if test="caddress != null and caddress != ''">
                caddress = #{caddress},
            </if>
        </set>
        where cid = #{cid}
    </update>

    <select id="selectAllCommunity" resultType="com.sam.entity.Community">
        select * from community where cid=#{cid}
    </select>

</mapper>