<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sam.mapper.BuildingMapper">
    <!--根据cid查询楼栋信息-->
    <select id="selectBuildingByCid" resultType="com.sam.entity.Building">
        select * from building where cid=#{cid}
    </select>


    <!--    需求3： 根据楼栋ID查询楼栋信息及对应小区信息-->
    <resultMap id="buildingCommunityMap" type="Building">
        <id property="did" column="did"/>
        <result property="height" column="height"/>
        <result property="cid" column="cid"/>
        <association property="community" column="cid"
                     javaType="com.sam.entity.Community"
                     select="com.sam.mapper.CommunityMapper.selectAllCommunity"/>
    </resultMap>

    <select id="selectBuildingByDid" resultMap="buildingCommunityMap">
        SELECT *
        FROM building
        LEFT JOIN community ON building.cid = community.cid
        WHERE building.did = #{did}
    </select>

</mapper>