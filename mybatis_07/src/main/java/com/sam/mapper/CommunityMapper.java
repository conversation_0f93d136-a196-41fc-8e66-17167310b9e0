package com.sam.mapper;

import com.sam.entity.Building;
import com.sam.entity.Community;
import org.apache.ibatis.annotations.Select;

public interface CommunityMapper {

    //需求1： 根据小区ID查询小区信息及对应楼栋信息（分步骤查询）
    Community selectCommunityAndBuilding(int cid);

    //需求2： 使用动态SQL对小区信息进行更改
    //Mybatis不能直接返回实体类
    int updateCommunity(Community community);

    //查询所有小区信息
//    @Select("select * from community where cid=#{cid}")
    Community selectAllCommunity(Integer cid);

}
