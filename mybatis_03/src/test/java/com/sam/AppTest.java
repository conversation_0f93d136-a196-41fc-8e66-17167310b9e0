package com.sam;

import com.sam.entity.Book;
import com.sam.mapper.BookMapper;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;

public class AppTest {

    private SqlSession sqlSession;
    private BookMapper bookMapper;

    @Before
    public void before() throws Exception {
        String resource = "mybatis-config.xml";
        InputStream inputStream = Resources.getResourceAsStream(resource);
        SqlSessionFactory sqlSessionFactory = new SqlSessionFactoryBuilder().build(inputStream);
        sqlSession = sqlSessionFactory.openSession();
        bookMapper = sqlSession.getMapper(BookMapper.class);
    }

    //查询测试
    @Test
    public void testBookList() {
        List<Book> books = bookMapper.bookList();
        books.forEach(System.out::println);
    }

    //新增测试
    @Test
    public void insertBook() {
        Book book = new Book();
        book.setTitle("红楼梦");
        book.setAuthor("曹雪芹");
        book.setPrice(BigDecimal.valueOf(120.00));
        book.setSales(100);
        book.setStock(100);
        book.setImg("红楼梦.jpg");
        int n = bookMapper.insertBook(book);
        sqlSession.commit();
        sqlSession.close();
        System.out.println("是否插入成功:"+n);
    }

    //修改测试
    @Test
    public void updateUserPriceAndStock() {
        Book book = new Book();
        book.setId(33);
        book.setPrice(BigDecimal.valueOf(111.11));
        book.setStock(666);
        int n = bookMapper.updateUserPriceAndStock(book);
        sqlSession.commit();
        sqlSession.close();
        System.out.println("是否修改成功:"+n);
    }

    //删除测试


    @Test
    public void deleteBook() {
        int n = bookMapper.deleteBook(33);
        sqlSession.commit();
        sqlSession.close();
        System.out.println("是否删除成功:"+n);
    }

    @After
    public void after() {
        sqlSession.close();
    }
}