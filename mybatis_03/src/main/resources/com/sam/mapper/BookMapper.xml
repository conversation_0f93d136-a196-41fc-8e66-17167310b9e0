<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sam.mapper.BookMapper">


    <!--    添加Book数据-->
    <insert id="insertBook" parameterType="book">
        insert into tb_book(title,author,price,sales,stock,img)
        values(#{title},#{author},#{price},#{sales},#{stock},#{img})
    </insert>

    <!--    删除Book数据-->
    <delete id="deleteBook" parameterType="int">
        delete from tb_book where id=#{id}
    </delete>

    <!--    根据图书id修改价格和库存-->
    <update id="updateUserPriceAndStock" parameterType="book">
        update tb_book set price=#{price},stock=#{stock} where id=#{id}
    </update>

    <!--    查询Book数据-->
    <select id="bookList" parameterType="int" resultType="book">
        select * from tb_book
    </select>
    <select id="selectBook" resultType="com.sam.entity.Book"></select>


</mapper>