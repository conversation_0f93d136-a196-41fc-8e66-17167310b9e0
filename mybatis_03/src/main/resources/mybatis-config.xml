<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>

    <!--  引入外部的属性文件       -->
    <properties resource="db.properties"></properties>

    <!--配置别名-->
    <typeAliases>
        <!-- 例如: com.sam.entity -->
        <package name="com.sam.entity"/>
    </typeAliases>

    <!-- 配置环境-->
    <environments default="development">
        <!-- id属性必须和上面的default一致 -->
        <environment id="development">
            <!--配置事务的类型-->
            <transactionManager type="JDBC"></transactionManager>
            <!--dataSource 元素使用标准的 JDBC 数据源接口来配置 JDBC 连接对象源 -->
            <dataSource type="POOLED">
                <!--配置连接数据库的4个基本信息-->
                <property name="driver" value="${database.driver}"/>
                <property name="url" value="${database.url}"/>
                <property name="username" value="${database.username}"/>
                <property name="password" value="${database.password}"/>
            </dataSource>
        </environment>
    </environments>
    <!--  指定映射文件位置 -->
    <mappers>
        <!--  例如: com.sam.mapper -->
        <package name="com.sam.mapper"/>
    </mappers>
</configuration>