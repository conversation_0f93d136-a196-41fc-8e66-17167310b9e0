package com.sam;


import com.sam.entity.User;
import com.sam.mapper.UserMapper;
import org.apache.ibatis.io.Resources;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.SqlSessionFactoryBuilder;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

public class AppTest
{
    SqlSession sqlSession;
    UserMapper userMapper;
    @Before
    public void before() throws Exception
    {
        System.out.println("----开始测试----");
        System.out.println("1.加载mybatis-config.xml和UserMapper.xml文件");
        String resource = "mybatis-config.xml";
        System.out.println("2.构建SqlSessionFactory(管理数据库会话的核心对象)");
        SqlSessionFactory sqlSessionFactory =
                new SqlSessionFactoryBuilder().build(Resources.getResourceAsStream(resource));
        System.out.println("3.从SqlSessionFactory中打开一个SqlSession");
        sqlSession = sqlSessionFactory.openSession();
        System.out.println("4.使用SqlSession创建UserMapper接口的代理对象");
        userMapper = sqlSession.getMapper(UserMapper.class);
    }

    @Test
    public  void testSelectUser(){
        System.out.println("5.调用UserMapper接口方法");
        User user = userMapper.selectUser(1);
        System.out.println("user = " + user);
    }

    @Test
    public void testInsertUser() {
        System.out.println("5.调用UserMapper接口方法");
        User user = new User();
        user.setUsername("老大");
        user.setAge(18);
        user.setSex("男");
        user.setAddress("上海");
        user.setStatus("正常");
        int n = userMapper.insertUser(user);
        System.out.println("插入成功");
    }

    @After
    public void after() throws Exception
    {
        System.out.println("6.调用 SqlSession.commit() 手动提交事务");
        sqlSession.commit();
        System.out.println("7.调用 SqlSession.close() 释放连接（归还连接池）");
        sqlSession.close();
        System.out.println("----结束测试----");
    }
}
