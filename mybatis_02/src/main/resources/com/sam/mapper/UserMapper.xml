<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sam.mapper.UserMapper">

    <!--    User selectUser(Integer id); -->
    <!--    要求1： id属性值必须与Mapper接口中的方法名一致 -->
    <!--    要求2：  parameterType的类型 和Mapper接口中的方法的输入参数类型相同，有时可以不配置-->
    <!--    要求3：  resultType的类型 和Mapper接口中的方法的返回值类型相同，有时可以不配置-->
    <select id="selectUser" parameterType="int" resultType="user">
        select * from tb_user where id=#{id}
    </select>

    <!--    int insertUser(User user);-->
    <insert id="insertUser" parameterType="user">
        insert into
        tb_user(username,age,birthday,sex,address, status)
        values(#{username},#{age},#{birthday},#{sex},#{address},#{status})
    </insert>

    <!--    int updateUser(User user);-->
    <update id="updateUser" parameterType="user">
        update tb_user
        set username=#{username},age=#{age},
        birthday=#{birthday},sex=#{sex},
        address=#{address},status=#{status}
        where id=#{id}
    </update>

    <!--    int deleteUser(Integer id);-->
    <delete id="deleteUser" parameterType="int">
        delete from tb_user where id=#{id}
    </delete>

    <!--    List<User> userList();-->
    <select id="userList" resultType="user">
        select * from tb_user
    </select>

    <!--    List<User>  selectUserByNameAndAge(String name,Integer age);-->
    <select id="selectUserByNameAndAge" resultType="user">
        select * from tb_user where username like concat('%',#{arg0},'%') or age= #{arg1}
    </select>

    <!--    long countUser();-->
    <select id="countUser" resultType="_long">
        select count(*) from tb_user
    </select>
</mapper>