package com.sam.mapper;

import com.sam.entity.User;

import java.util.List;

public interface UserMapper {
    //根据id查询单个用户信息
    User selectUser(Integer id);

    //插入用户信息
    int insertUser(User user);

    //修改用户信息
    int updateUser(User user);

    //根据ID删除用户信息
    int deleteUser(Integer id);

    //查询所有用户信息
    List<User> userList();

    //根据条件查询
    List<User>  selectUserByNameAndAge(String name,Integer age);

    //统计人数
    long countUser();
}
